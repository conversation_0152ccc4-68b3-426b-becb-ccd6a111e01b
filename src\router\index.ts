import { createRouter, createWebHistory } from 'vue-router'

export default createRouter({
  history: createWebHistory(),
  routes: [
    { name: 'home', path: '/', component: () => import('@/pages/home/<USER>') },
    { name: 'hospital', path: '/hospital', component: () => import('@/pages/hospital/index.vue'), children:[
      { name: 'register', path: 'register', component: () => import('@/pages/hospital/register/index.vue') },
      { name: 'detail', path: 'detail', component: () => import('@/pages/hospital/detail/index.vue') },
      { name: 'notice', path: 'notice', component: () => import('@/pages/hospital/notice/index.vue') },
      { name: 'close', path: 'close', component: () => import('@/pages/hospital/close/index.vue') },
      { name: 'search', path: 'search', component: () => import('@/pages/hospital/search/index.vue') }
    ]}
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  }
})