<script setup lang="ts">
import Banner from './banner/index.vue'
import Search from './search/index.vue'
import Option from './option/index.vue'
import Card from './card/index.vue'
import Tip from './tip/index.vue'

const levels = ref<string[]>([])
const levelValues = ref<string[]>([])
const regions = ref<string[]>([])
const regionValues = ref<string[]>([])
import { getHospitalList, getHospitalLevels } from '@/api/home'
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import type { Content, HospitalResponseData, HospitalLevenAndRegionResponseData } from '@/api/home/<USER>'

const hospitals = ref<Content>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)
const level = ref<string>('')
const region = ref<string>('')

onMounted(() => {
  getHospitalLevelInfo()
  getHospitalRegionInfo()
  getHospitalInfo()
})

async function getHospitalInfo() {
  try {
    const res: HospitalResponseData = await getHospitalList(currentPage.value, pageSize.value, level.value, region.value)
    total.value = res.data.totalElements
    hospitals.value = res.data.content
  } catch (error) {
    ElMessage.error('获取医院列表失败')
  }
}

async function getHospitalLevelInfo() {
  try {
    const res: HospitalLevenAndRegionResponseData = await getHospitalLevels('HosType')
    for (const h of res.data) {
      levels.value.push(h.name)
      levelValues.value.push(h.value)
    }
  } catch (error) {
    ElMessage.error('获取医院等级失败')
  }
}

async function getHospitalRegionInfo() {
  try {
    const res: HospitalLevenAndRegionResponseData = await getHospitalLevels('Beijin')
    for (const h of res.data) {
      regions.value.push(h.name)
      regionValues.value.push(h.value)
    }
  } catch (error) {
    ElMessage.error('获取医院地区失败')
  }
}

const levelChanged = (level_: string) => {
  level.value = level_ ? level_ : ''
  getHospitalInfo()
}

const regionChanged = (region_: string) => {
  region.value = region_ ? region_ : ''
  getHospitalInfo()
}

</script>

<template>
  <Banner />
  <Search />
  <el-row :gutter="38">
    <el-col :span="19" class="option">
      <span>医院</span>
      <Option name="等级" :options="levels" :values="levelValues" @option-changed="levelChanged" />
      <Option name="地区" :options="regions" :values="regionValues" @option-changed="regionChanged" />
      <div class="hospital" v-if="hospitals.length > 0">
        <Card class="card" v-for="hospital in hospitals" :key="hospital.id" :name="hospital.hosname"
          :level="hospital.param.hostypeString" :time="hospital.bookingRule.releaseTime" :logo="hospital.logoData" @click="$router.push({name: 'hospital', query: {hoscode: hospital.hoscode}})"/>
        <el-pagination class="page" v-model:current-page="currentPage" v-model:page-size="pageSize"
          :page-sizes="[5, 10, 15, 20]" layout="prev, pager, next, ->, sizes, total" :total="total"
          @current-change="getHospitalInfo" @size-change="currentPage = 1; getHospitalInfo()" />
      </div>
      <el-empty v-else description="暂无数据"></el-empty>
    </el-col>
    <el-col :span="5">
      <Tip/>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.option {
  span {
    color: #7f7f7f;
    display: inline-block;
    margin: 10px 0;
  }
}

.hospital {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;

  .card {
    width: 48%;
  }

  .page {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
