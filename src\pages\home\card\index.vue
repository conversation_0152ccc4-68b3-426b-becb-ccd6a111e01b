<script setup lang="ts">
defineProps(['name', 'level', 'time', 'logo'])
</script>

<template>
  <el-card shadow="hover">
    <div class="content">
      <div class="left">
        <div class="name">
          {{ name }}
        </div>
        <div class="info">
          <span><svg t="1754986377489" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3203" width="16" height="16"><path d="M832 364.8h-147.2s19.2-64 32-179.2c6.4-57.6-38.4-115.2-102.4-121.6h-12.8c-51.2 0-83.2 32-102.4 76.8l-38.4 96c-32 64-57.6 102.4-76.8 115.2-25.6 12.8-121.6 12.8-128 12.8H128c-38.4 0-64 25.6-64 57.6v480c0 32 25.6 57.6 64 57.6h646.4c96 0 121.6-64 134.4-153.6l51.2-307.2c6.4-70.4-6.4-134.4-128-134.4z m-576 537.6H128V422.4h128v480z m640-409.6l-51.2 307.2c-12.8 57.6-12.8 102.4-76.8 102.4H320V422.4c44.8 0 70.4-6.4 89.6-19.2 32-12.8 64-64 108.8-147.2 25.6-64 38.4-96 44.8-102.4 6.4-19.2 19.2-32 44.8-32h6.4c32 0 44.8 32 44.8 51.2-12.8 102.4-32 166.4-32 166.4l-25.6 83.2h243.2c19.2 0 32 0 44.8 12.8 12.8 12.8 6.4 38.4 6.4 57.6z" p-id="3204"></path></svg>{{ level }}</span>
          <span><svg t="1754986618592" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4393" width="16" height="16"><path d="M512 178.1c-12.3 0-22.3 10-22.3 22.3V512c0 6.5 2.9 12.3 7.3 16.4 0.2 0.2 0.4 0.5 0.6 0.8L655 686.6c4.3 4.3 10 6.5 15.7 6.5 5.7 0 11.4-2.2 15.7-6.5 8.7-8.7 8.7-22.8 0-31.5L534.3 502.8V200.3c0-12.2-10-22.2-22.3-22.2z" fill="" p-id="4394"></path><path d="M1001.7 489.7c-12.3 0-22.3 10-22.3 22.3 0 257.8-209.7 467.5-467.5 467.5S44.5 769.8 44.5 512 254.2 44.5 512 44.5c168.9 0 325.3 91.6 408 239.2 5 8.8 15.3 13.2 25 10.6 9.8-2.6 16.6-11.4 16.6-21.5V102.4c0-12.3-10-22.3-22.3-22.3s-22.3 10-22.3 22.3V199C821 74.7 671.6 0 512 0 229.7 0 0 229.7 0 512s229.7 512 512 512 512-229.7 512-512c0-12.3-10-22.3-22.3-22.3z" fill="" p-id="4395"></path></svg>每天{{ time }}放号</span>
        </div>
      </div>
      <div class="right">
        <img :src="`data:image/jpeg;base64,${logo}`" alt="">
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  justify-content: space-between;
  .left {
    width: 60%;
    .name {
      font-weight: 400;
      margin-bottom: 15px;
    }
    .info {
      span {
        display: flex;
        align-items: center;
        gap: 4px;
      }
      display: flex;
      justify-content: space-between;
      font-size: 15px;
      color: #7f7f7f;
      font-weight: 300;
    }
  }
  .right {
    img {
      width: 50px;
      height: 50px;
    }
  }
}
</style>