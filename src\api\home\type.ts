interface ResponseData {
  code: number,
  message: string,
  ok: boolean
}

interface Hospital {
  address: string,
  bookingRule: {
    cycle: number,
    quitDay: number,
    quitTime: string,
    releaseTime: string
  },
  cityCode: string,
  createTime: string,
  districtCode: string,
  hoscode: string,
  hosname: string,
  hostype: string,
  id: string,
  intro: string,
  isDeleted: number,
  logoData: string,
  param: {
    fullAddress: string,
    hostypeString: string
  },
  provinceCode: string,
  route: string,
  status: number,
  updateTime: string,
}

export type Content = Hospital[]

export interface HospitalResponseData extends ResponseData {
  data: {
    content: Content,
    empty: boolean,
    first: boolean,
    last: boolean,
    number: number,
    numberOfElements: number,
    pageable: {
      offset: number,
      pageNumber: number,
      pageSize: number,
      paged: boolean,
      sort: {
        empty: boolean,
        sorted: boolean,
        unsorted: boolean
      },
      unpaged: boolean
    },
    size: number,
    sort: {
      empty: boolean,
      sorted: boolean,
      unsorted: boolean
    }
    totalElements: number,
    totalPages: number
  }
}

interface HospitalLevelAndRegion {
  id: number,
  createTime:string,
  updateTime:string,
  isDeleted:number,
  param:{},
  parentId:number,
  name: string,
  value:string,
  dictCode:string,
  hasChildren:boolean
}

type HospitalLevelAndRegionArr = HospitalLevelAndRegion[]

export interface HospitalLevenAndRegionResponseData extends ResponseData {
  data:HospitalLevelAndRegionArr
}

export interface HospitalByNameResponseData extends ResponseData {
  data: Content
}