<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { getHospitalsByName } from '@/api/home'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const hosname = ref<string>('')
const fetchData = async (keyword: string, cb: Function) => {
  try {
    let res = await getHospitalsByName(keyword)
    let showData = res.data.map((item) => {
      return {
        value: item.hosname,
        hoscode: item.hoscode
      }
    })
    cb(showData)
  } catch (error:any) {
    ElMessage('搜索医院失败：'+error)
  }
  
}
let $router = useRouter()
const handleSelect = (item:any)=>{
  $router.push({name: 'hospital',
  query: {hoscode:item.hoscode}})
}
</script>

<template>
  <div class="search">
    <el-autocomplete clearable placeholder="请输入医院名称" class="input" v-model="hosname" :fetch-suggestions="fetchData" @select="handleSelect"
      :trigger-on-focus="false" />
    <el-button type="primary" :icon="Search"></el-button>
  </div>
</template>

<style scoped lang="scss">
.search {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;

  :deep(.el-autocomplete) {
    width: 600px;
    margin-right: 10px;

    .el-input {
      height: 50px;
      font-size: 16px;
    }
  }

  :deep(.el-button) {
    height: 50px;
    width: 50px;

    .el-icon {
      font-size: 20px;
    }
  }
}
</style>
