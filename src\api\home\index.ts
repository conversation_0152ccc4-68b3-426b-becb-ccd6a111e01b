import request from '@/utils/request'
import type { HospitalResponseData, HospitalLevenAndRegionResponseData, HospitalByNameResponseData } from './type'

export const getHospitalList = (page: number, limit: number, level:string='', region:string='') => {
  return request.get<void, HospitalResponseData>(`/hosp/hospital/${page}/${limit}?hostype=${level}&districtCode=${region}`)
}

export const getHospitalLevels = (dictCode:string) => {
  return request.get<void, HospitalLevenAndRegionResponseData>('/cmn/dict/findByDictCode/' + dictCode)
}

export const getHospitalsByName = (hosname:string) => {
  return request.get<void, HospitalByNameResponseData>('/hosp/hospital/findByHosname/' + hosname)
}