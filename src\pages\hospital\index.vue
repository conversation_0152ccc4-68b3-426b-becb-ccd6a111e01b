<script setup lang="ts">
import { HomeFilled, Calendar, Document, Message, InfoFilled, Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()

// 根据当前路由计算激活的菜单项
const activeIndex = computed(() => {
  const routeName = route.name as string
  return routeName || 'register' // 默认为 register
})
</script>

<template>
  <div class="top">
      <el-icon><HomeFilled /></el-icon><span>&nbsp;/ 医院信息</span>
  </div>
  <div class="hospital">
    <el-menu :default-active="activeIndex" class="navigator">
      <el-menu-item index="register" @click="$router.push({name: 'register'})">
        <el-icon><Calendar /></el-icon>
        <span>预约挂号</span>
      </el-menu-item>
      <el-menu-item index="detail" @click="$router.push({name: 'detail'})">
        <el-icon><Document /></el-icon>
        <span>医院详情</span>
      </el-menu-item>
      <el-menu-item index="notice" @click="$router.push({name: 'notice'})">
        <el-icon><Message /></el-icon>
        <span>预约须知</span>
      </el-menu-item>
      <el-menu-item index="close" @click="$router.push({name: 'close'})">
        <el-icon><InfoFilled /></el-icon>
        <span>停诊信息</span>
      </el-menu-item>
      <el-menu-item index="search" @click="$router.push({name: 'search'})">
        <el-icon><Search /></el-icon>
        <span>查询/取消</span>
      </el-menu-item>
    </el-menu>
    <div class="content">
      <router-view></router-view>
    </div>
  </div>
</template>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    color: #7f7f7f;
    margin: 20px 0 10px 20px;
    font-size: 14px;
  }

.hospital {
  display: flex;

  .navigator {
    flex: 2;
  }

  .content {
    flex: 8;
  }
}
</style>