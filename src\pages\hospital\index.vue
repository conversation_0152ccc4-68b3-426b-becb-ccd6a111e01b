<script setup lang="ts">
import { HomeFilled, Calendar, Document, Message, InfoFilled, Search } from '@element-plus/icons-vue'
</script>

<template>
  <div class="top">
      <el-icon><HomeFilled /></el-icon><span>&nbsp;/ 医院信息</span>
  </div>
  <div class="hospital">
    <el-menu default-active="1" class="navigator">
      <el-menu-item index="1">
        <el-icon><Calendar /></el-icon>
        <span>预约挂号</span>
      </el-menu-item>
      <el-menu-item index="2">
        <el-icon><Document /></el-icon>
        <span>医院详情</span>
      </el-menu-item>
      <el-menu-item index="3">
        <el-icon><Message /></el-icon>
        <span>预约须知</span>
      </el-menu-item>
      <el-menu-item index="4">
        <el-icon><InfoFilled /></el-icon>
        <span>停诊信息</span>
      </el-menu-item>
      <el-menu-item index="5">
        <el-icon><Search /></el-icon>
        <span>查询/取消</span>
      </el-menu-item>
    </el-menu>
    <div class="content">

    </div>
  </div>
</template>

<style scoped lang="scss">
.top {
    display: flex;
    align-items: center;
    color: #7f7f7f;
    margin: 20px 0 10px 20px;
    font-size: 14px;
  }

.hospital {
  display: flex;

  .navigator {
    flex: 2;
  }

  .content {
    flex: 8;
  }
}
</style>